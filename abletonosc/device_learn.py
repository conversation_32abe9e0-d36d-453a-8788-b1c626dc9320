import logging
from .utils.parameter_throttler import ParameterThrottler

class DeviceLearn:
    def __init__(self, device_handler):
        self.device_handler = device_handler
        self.manager = device_handler.manager
        self.osc_server = device_handler.osc_server
        self.song = device_handler.song
        self.logger = logging.getLogger("abletonosc")
        
        self.learn_listeners = {}
        self.parameter_throttler = ParameterThrottler()

    def setup_learn_listeners(self, *args):
        """Configure les listeners pour tous les devices de la piste sélectionnée"""
        # Marquer l'état en cours de reconfiguration pour éviter les appels récursifs
        if hasattr(self, '_reconfiguring') and self._reconfiguring:
            self.logger.debug("Reconfiguration déjà en cours, ignoré")
            return
        
        self._reconfiguring = True
        try:
            # Arrêter les listeners existants d'abord
            self.stop_learn_listeners(None)
            self.manager.learn_mode = True
            
            selected_track = self.song.view.selected_track
            if not selected_track:
                self.logger.warning("Aucune piste sélectionnée")
                self.osc_server.send("/live/readyToListen")
                return

            visible_devices = self.manager.get_visible_devices(selected_track)
            
            # Configurer les listeners pour surveiller les changements de structure
            self._setup_track_structure_listeners(selected_track)
            
            for device_index, device in enumerate(visible_devices):
                self._setup_device_learn_listeners(device, device_index)
                # Ajouter la configuration des listeners pour les chaînes si le device est un rack
                if hasattr(device, 'chains') and device.chains:
                    # Initialiser le chemin avec l'index du rack
                    # Le chemin est une liste qui représente la hiérarchie des racks et chaînes
                    rack_path = [device_index]
                    self._setup_chains_learn_listeners(device, rack_path)

            self.osc_server.send("/live/readyToListen")
        finally:
            self._reconfiguring = False

    def _setup_track_structure_listeners(self, track):
        """Configure des listeners pour détecter les changements de structure des chaînes dans la piste"""
        
        # Listener pour le changement de device sélectionné
        def on_selected_device_changed():
            if not on_selected_device_changed.is_active:
                return
            self.logger.debug("Device sélectionné changé - reconstruction des listeners")
            self.setup_learn_listeners()
            
        on_selected_device_changed.is_active = True
        listener_key = "track_selected_device_change"
        
        if listener_key in self.learn_listeners:
            old_callback = self.learn_listeners[listener_key]
            old_callback.is_active = False
            try:
                if track.view.selected_device_has_listener(old_callback):
                    track.view.remove_selected_device_listener(old_callback)
            except RuntimeError:
                self.logger.debug(f"Le listener pour {listener_key} n'était plus connecté")
        
        self.learn_listeners[listener_key] = on_selected_device_changed
        track.view.add_selected_device_listener(on_selected_device_changed)
        
        # Configurer des listeners pour chaque rack dans la piste
        visible_devices = self.manager.get_visible_devices(track)
        for device_index, device in enumerate(visible_devices):
            if hasattr(device, 'chains'):
                # Ajouter un listener direct sur les chaînes du device
                def create_chains_callback(dev, idx):
                    def on_chains_changed():
                        self.logger.debug(f"Changement détecté dans les chaînes du rack {dev.name}")
                        # Si le rack n'avait pas de chaînes avant et en a maintenant
                        if len(dev.chains) > 0:
                            self.logger.debug(f"Le rack {dev.name} a maintenant {len(dev.chains)} chaînes")
                            self.setup_learn_listeners()
                    return on_chains_changed
                    
                chains_callback = create_chains_callback(device, device_index)
                listener_key = f"device_{device_index}_chains"
                
                # Supprimer l'ancien listener s'il existe
                if listener_key in self.learn_listeners:
                    old_callback = self.learn_listeners[listener_key]
                    old_callback.is_active = False
                    try:
                        if device.chains_has_listener(old_callback):
                            device.remove_chains_listener(old_callback)
                    except RuntimeError:
                        self.logger.debug(f"Le listener pour {listener_key} n'était plus connecté")
                
                # Ajouter le nouveau listener
                chains_callback.is_active = True
                chains_callback.parameter = device
                self.learn_listeners[listener_key] = chains_callback
                device.add_chains_listener(chains_callback)
                
                # Continuer avec la configuration normale des listeners de structure
                self._setup_rack_structure_listeners(device, device_index)

    def _setup_rack_structure_listeners(self, rack_device, device_index, parent_path=None):
        """Configure des listeners pour détecter les changements de structure dans un rack"""
        path = [device_index] if parent_path is None else parent_path + [device_index]
        
        # Listener pour le changement de chaîne sélectionnée
        def on_selected_chain_changed():
            if not on_selected_chain_changed.is_active:
                return
            self.logger.debug(f"Chaîne sélectionnée changée dans le rack {rack_device.name} - reconstruction des listeners")
            self.setup_learn_listeners()
        
        on_selected_chain_changed.is_active = True
        on_selected_chain_changed.parameter = rack_device
        listener_key = f"rack_selected_chain_change_{'.'.join(map(str, path))}"
        
        if listener_key in self.learn_listeners:
            old_callback = self.learn_listeners[listener_key]
            old_callback.is_active = False
            try:
                if rack_device.view.selected_chain_has_listener(old_callback):
                    rack_device.view.remove_selected_chain_listener(old_callback)
            except RuntimeError:
                self.logger.debug(f"Le listener pour {listener_key} n'était plus connecté")
        
        self.learn_listeners[listener_key] = on_selected_chain_changed
        rack_device.view.add_selected_chain_listener(on_selected_chain_changed)
        
        # Listener CRUCIAL pour les changements dans la liste des chaînes
        # Ceci détectera l'ajout/suppression d'une chaîne, y compris le passage de 0 à 1 chaîne
        def on_chains_changed():
            if not on_chains_changed.is_active:
                return
            
            # Récupérer l'état actuel des chaînes
            chains_count = len(rack_device.chains)
            
            self.logger.debug(f"Structure des chaînes changée dans {rack_device.name} - nombre de chaînes: {chains_count}")
            
            # Reconfigurer tous les listeners quand les chaînes changent
            # C'est crucial quand on passe de 0 à 1 chaîne ou inversement
            self.setup_learn_listeners()
        
        on_chains_changed.is_active = True
        on_chains_changed.parameter = rack_device
        listener_key = f"rack_chains_change_{'.'.join(map(str, path))}"
        
        if listener_key in self.learn_listeners:
            old_callback = self.learn_listeners[listener_key]
            old_callback.is_active = False
            try:
                if rack_device.chains_has_listener(old_callback):
                    rack_device.remove_chains_listener(old_callback)
            except RuntimeError:
                self.logger.debug(f"Le listener pour {listener_key} n'était plus connecté")
        
        self.learn_listeners[listener_key] = on_chains_changed
        
        # Ajouter le listener pour détecter les changements dans la liste des chaînes
        try:
            # Cette ligne est cruciale - c'est celle qui détecte l'ajout de la première chaîne
            rack_device.add_chains_listener(on_chains_changed)
            self.logger.debug(f"Listener 'chains_listener' ajouté au rack {rack_device.name}")
        except Exception as e:
            self.logger.error(f"Erreur lors de l'ajout du chains_listener: {str(e)}")
        
        # Vérifier si ce rack a des chaînes
        if rack_device.chains:
            # Configurer récursivement des listeners pour chaque chaîne du rack existante
            for chain_index, chain in enumerate(rack_device.chains):
                chain_path = path + [chain_index]
                
                # Parcourir récursivement les devices de cette chaîne pour trouver d'autres racks
                for nested_device_index, nested_device in enumerate(chain.devices):
                    if hasattr(nested_device, 'chains') and nested_device.chains is not None:
                        nested_path = chain_path + [nested_device_index]
                        self._setup_rack_structure_listeners(nested_device, nested_device_index, chain_path)
        else:
            # Cas spécial: rack vide, on s'assure que le listener chains est bien en place
            self.logger.debug(f"Rack {rack_device.name} sans chaînes - listener chains configuré")

    def _setup_device_learn_listeners(self, device, device_index):
        """Configure les listeners pour un device spécifique"""
        # Envoyer le nom du device
        self.osc_server.send("/live/device/learning/name", (-2, device_index, device.name))

        # Récupérer les informations des paramètres
        parameters = device.parameters
        bulk_params = []
        for param in parameters:
            bulk_params.extend([
                param.name,
                param.is_quantized,
                param.min,
                param.max
            ])

        # Envoyer toutes les informations en un seul message
        self.osc_server.send("/live/device/learning/bulk_parameters", (-2, device_index, *bulk_params))

        # Configurer les listeners pour chaque paramètre
        for param_index, parameter in enumerate(device.parameters):
            self._setup_parameter_listener(device_index, param_index, parameter)

    def _setup_chains_learn_listeners(self, rack_device, rack_path):
        """Configure les listeners pour toutes les chaînes d'un rack, de manière récursive"""
        if not rack_path:
            self.logger.warning("_setup_chains_learn_listeners appelé avec un chemin vide")
            return

        for chain_index, chain in enumerate(rack_device.chains):
            # Créer le chemin complet pour cette chaîne
            chain_path = rack_path + [chain_index]
            
            self.logger.debug(f"Configurant listeners pour la chaîne avec le chemin: {chain_path}")
            
            # Configurer les listeners pour le volume, panning, mute et solo de cette chaîne
            self._setup_chain_volume_listener(chain, chain_path)
            self._setup_chain_panning_listener(chain, chain_path)
            self._setup_chain_mute_listener(chain, chain_path)
            self._setup_chain_solo_listener(chain, chain_path)
            
            # Parcourir récursivement les devices de cette chaîne
            for nested_device_index, nested_device in enumerate(chain.devices):
                if hasattr(nested_device, 'chains') and nested_device.chains:
                    nested_rack_path = chain_path + [nested_device_index]
                    self._setup_chains_learn_listeners(nested_device, nested_rack_path)

    def _setup_chain_volume_listener(self, chain, chain_path):
        """Configure un listener pour le volume d'une chaîne spécifique"""
        volume_param = chain.mixer_device.volume
        
        def callback():
            if not callback.is_active:
                return
                
            # Créer un identifiant unique pour le volume de cette chaîne en mode learn
            param_id = f"learn_chain_volume_{'.'.join(map(str, chain_path))}"
            
            def send_volume_update(value):
                self.osc_server.send("/live/chain/learning/volume", 
                                   (-2, chain_path, value))
                                   
            self.parameter_throttler.update_parameter(param_id, volume_param.value, send_volume_update)
        
        callback.is_active = True
        callback.parameter = volume_param
        
        # Créer une clé unique pour ce listener
        listener_key = f"chain_volume_{'.'.join(map(str, chain_path))}"
        
        # Supprimer l'ancien listener s'il existe
        if listener_key in self.learn_listeners:
            old_callback = self.learn_listeners[listener_key]
            old_callback.is_active = False
            try:
                if volume_param.value_has_listener(old_callback):
                    volume_param.remove_value_listener(old_callback)
            except RuntimeError:
                self.logger.debug(f"Le listener pour {listener_key} n'était plus connecté")
        
        # Ajouter le nouveau listener
        self.learn_listeners[listener_key] = callback
        volume_param.add_value_listener(callback)
        

    def _setup_chain_panning_listener(self, chain, chain_path):
        """Configure un listener pour le panoramique d'une chaîne spécifique"""
        panning_param = chain.mixer_device.panning
        
        def callback():
            if not callback.is_active:
                return
                
            # Créer un identifiant unique pour le pan de cette chaîne en mode learn
            param_id = f"learn_chain_pan_{'.'.join(map(str, chain_path))}"
            
            def send_pan_update(value):
                self.osc_server.send("/live/chain/learning/panning", 
                                   (-2, chain_path, value))
                                   
            self.parameter_throttler.update_parameter(param_id, panning_param.value, send_pan_update)
        
        callback.is_active = True
        callback.parameter = panning_param
        
        # Créer une clé unique pour ce listener
        listener_key = f"chain_panning_{'.'.join(map(str, chain_path))}"
        
        # Supprimer l'ancien listener s'il existe
        if listener_key in self.learn_listeners:
            old_callback = self.learn_listeners[listener_key]
            old_callback.is_active = False
            try:
                if panning_param.value_has_listener(old_callback):
                    panning_param.remove_value_listener(old_callback)
            except RuntimeError:
                self.logger.debug(f"Le listener pour {listener_key} n'était plus connecté")
        
        # Ajouter le nouveau listener
        self.learn_listeners[listener_key] = callback
        panning_param.add_value_listener(callback)
       

    def _setup_chain_mute_listener(self, chain, chain_path):
        """Configure un listener pour le mute d'une chaîne spécifique"""
        def callback():
            if not callback.is_active:
                return
                
            # Envoyer directement la valeur sans passer par le throttler
            value = 1 if chain.mute else 0
            self.logger.debug(f"Chain {chain_path} mute callback: {value}")
            self.osc_server.send("/live/chain/learning/mute", (-2, chain_path, value))
        
        callback.is_active = True
        callback.parameter = chain  # On garde la référence à la chaîne
        
        # Créer une clé unique pour ce listener
        listener_key = f"chain_mute_{'.'.join(map(str, chain_path))}"
        
        # Supprimer l'ancien listener s'il existe
        if listener_key in self.learn_listeners:
            old_callback = self.learn_listeners[listener_key]
            old_callback.is_active = False
            try:
                if chain.mute_has_listener(old_callback):
                    chain.remove_mute_listener(old_callback)
            except RuntimeError:
                self.logger.debug(f"Le listener pour {listener_key} n'était plus connecté")
        
        # Ajouter le nouveau listener
        self.learn_listeners[listener_key] = callback
        chain.add_mute_listener(callback)
        

    def     _setup_chain_solo_listener(self, chain, chain_path):
        """Configure un listener pour le solo d'une chaîne spécifique"""
        def callback():
            if not callback.is_active:
                return
                
            # Envoyer directement la valeur sans passer par le throttler
            value = 1 if chain.solo else 0
            self.logger.debug(f"Chain {chain_path} solo callback: {value}")
            self.osc_server.send("/live/chain/learning/solo", (-2, chain_path, value))
        
        callback.is_active = True
        callback.parameter = chain  # On garde la référence à la chaîne
        
        # Créer une clé unique pour ce listener
        listener_key = f"chain_solo_{'.'.join(map(str, chain_path))}"
        
        # Supprimer l'ancien listener s'il existe
        if listener_key in self.learn_listeners:
            old_callback = self.learn_listeners[listener_key]
            old_callback.is_active = False
            try:
                if chain.solo_has_listener(old_callback):
                    chain.remove_solo_listener(old_callback)
            except RuntimeError:
                self.logger.debug(f"Le listener pour {listener_key} n'était plus connecté")
        
        # Ajouter le nouveau listener
        self.learn_listeners[listener_key] = callback
        chain.add_solo_listener(callback)
        

    def _setup_parameter_listener(self, device_index, param_index, parameter):
        """Configure un listener pour un paramètre spécifique"""
        def callback():
            if not callback.is_active:
                return
                
            # Créer un identifiant unique pour ce paramètre en mode learn
            param_id = f"learn_device_{device_index}_param_{param_index}"
            
            def send_param_update(value):
                value_string = parameter.str_for_value(value)
                self.osc_server.send("/live/device/learning/parameter/value", 
                                   (-2, device_index, param_index, value, value_string))
            
            self.parameter_throttler.update_parameter(param_id, parameter.value, send_param_update)
        
        callback.is_active = True
        callback.parameter = parameter
        
        listener_key = f"device_{device_index}_param_{param_index}"
        if listener_key in self.learn_listeners:
            old_callback = self.learn_listeners[listener_key]
            old_callback.is_active = False
            # Ajouter une vérification si le listener est toujours connecté
            try:
                if parameter.value_has_listener(old_callback):
                    parameter.remove_value_listener(old_callback)
            except RuntimeError:
                self.logger.debug(f"Le listener pour {listener_key} n'était plus connecté")
        
        self.learn_listeners[listener_key] = callback
        parameter.add_value_listener(callback)
        

    def stop_learn_listeners(self, *args):
        """Arrête tous les listeners configurés pour le mode learn"""
        self.manager.learn_mode = False
        
        if not self.learn_listeners:
            return
        
        for key, callback in list(self.learn_listeners.items()):
            try:
                callback.is_active = False
                if hasattr(callback, 'parameter') and callback.parameter:
                    # Pour les listeners de paramètres standards
                    if hasattr(callback.parameter, 'value_has_listener') and callback.parameter.value_has_listener(callback):
                        callback.parameter.remove_value_listener(callback)
                    # Pour les listeners de mute
                    elif hasattr(callback.parameter, 'mute_has_listener') and callback.parameter.mute_has_listener(callback):
                        callback.parameter.remove_mute_listener(callback)
                    # Pour les listeners de solo
                    elif hasattr(callback.parameter, 'solo_has_listener') and callback.parameter.solo_has_listener(callback):
                        callback.parameter.remove_solo_listener(callback)
                    # Pour les listeners de structure
                    elif 'track_selected_device_change' in key and hasattr(callback.parameter, 'view'):
                        if callback.parameter.view.selected_device_has_listener(callback):
                            callback.parameter.view.remove_selected_device_listener(callback)
                    elif 'rack_selected_chain_change' in key and hasattr(callback.parameter, 'view'):
                        if callback.parameter.view.selected_chain_has_listener(callback):
                            callback.parameter.view.remove_selected_chain_listener(callback)
                    elif 'rack_chains_change' in key and hasattr(callback.parameter, 'chains_has_listener'):
                        if callback.parameter.chains_has_listener(callback):
                            callback.parameter.remove_chains_listener(callback)
            except Exception as e:
                self.logger.error(f"Erreur lors de la suppression du listener pour {key}: {str(e)}")
        
        self.learn_listeners.clear()

    def disable_parameter_listener(self, device, param_index):
        """Désactive temporairement un listener spécifique pour éviter les conflits avec learn mode"""
        try:
            # Chercher le listener correspondant
            for key, callback in self.learn_listeners.items():
                if (key.startswith("device_") and key.endswith(f"_param_{param_index}") and
                    hasattr(callback, 'parameter') and callback.parameter in device.parameters):
                    if device.parameters.index(callback.parameter) == param_index:
                        callback.is_active = False
                        self.logger.info(f"Listener device_learn désactivé temporairement: {key}")
                        return key
        except Exception as e:
            self.logger.error(f"Erreur lors de la désactivation du listener: {e}")
        return None

    def enable_parameter_listener(self, listener_key):
        """Réactive un listener précédemment désactivé"""
        try:
            if listener_key in self.learn_listeners:
                callback = self.learn_listeners[listener_key]
                callback.is_active = True
                self.logger.info(f"Listener device_learn réactivé: {listener_key}")
        except Exception as e:
            self.logger.error(f"Erreur lors de la réactivation du listener: {e}")

    def handle_device_learning(self, params):
        """
        Gère les messages reçus sur /live/device/learning/parameter/value
        params: (-3, device_index, param_index)
        """
        try:
            # Extraction des paramètres et conversion en entiers
            track_id = int(params[0])
            device_index = int(params[1])
            param_index = int(params[2])
            
            # Récupération de la piste sélectionnée
            selected_track = self.song.view.selected_track
            if not selected_track:
                self.logger.warning("Aucune piste sélectionnée")
                return

            # Gestion spéciale pour le lockedDevice (device_index = -4)
            if device_index == -4:
                if not self.manager.deviceLock or not self.manager.lockedDevice:
                    self.logger.warning("Pas de device verrouillé")
                    return
                device = self.manager.lockedDevice
            else:
                # Comportement normal pour les autres device_index
                visible_devices = self.manager.get_visible_devices(selected_track)
                if device_index >= len(visible_devices):
                    self.logger.warning(f"Index de device invalide: {device_index}")
                    return
                device = visible_devices[device_index]
            
            # Vérification de l'index du paramètre
            if param_index >= len(device.parameters):
                self.logger.warning(f"Index de paramètre invalide: {param_index}")
                return
            
            parameter = device.parameters[param_index]
            
            # Envoi des propriétés supplémentaires pour le lockedDevice (pour le learn par touch)
            if device_index == -4:
                param_min = parameter.min
                param_max = parameter.max
                param_is_quantized = parameter.is_quantized 
                param_name = parameter.name
                self.osc_server.send("/live/get/lockedDevice/paramProperties",
                                   (-2, -4, param_min, param_max, param_is_quantized, param_name))
                       
             # Envoi des informations du paramètre
            value = parameter.value
            value_string = parameter.str_for_value(value)
            self.osc_server.send("/live/device/learning/parameter/value", 
                               (-2, device_index, param_index, value, value_string))
        
        except Exception as e:
            self.logger.error(f"Erreur dans handle_device_learning: {str(e)}")

    def handle_chain_volume_learning(self, params):
        """
        Gère les messages reçus sur /live/chain/learning/volume
        params: (-2, chain_path)
        """
        try:
            # Extraire le chemin de la chaîne
            track_id = int(params[0])
            chain_path = params[1]  # chain_path est une liste d'indices
            
            # Récupérer la chaîne à partir du chemin
            chain = self._get_chain_by_path(chain_path)
            if not chain:
                self.logger.warning(f"Chaîne introuvable avec le chemin: {chain_path}")
                return

            # Obtenir la valeur du volume
            volume = chain.mixer_device.volume.value

            # Envoi du message avec la valeur du volume et le chemin de la chaîne
            self.osc_server.send("/live/chain/learning/volume", (-2, chain_path, volume))

        except Exception as e:
            self.logger.error(f"Erreur dans handle_chain_volume_learning: {str(e)}")

    def handle_chain_panning_learning(self, params):
        """
        Gère les messages reçus sur /live/chain/learning/panning
        params: (-2, chain_path)
        """
        try:
            # Extraire le chemin de la chaîne
            track_id = int(params[0])
            chain_path = params[1]  # chain_path est une liste d'indices
            
            # Récupérer la chaîne à partir du chemin
            chain = self._get_chain_by_path(chain_path)
            if not chain:
                self.logger.warning(f"Chaîne introuvable avec le chemin: {chain_path}")
                return

            # Obtenir la valeur du panning
            panning = chain.mixer_device.panning.value

            # Envoi du message avec la valeur du panning et le chemin de la chaîne
            self.osc_server.send("/live/chain/learning/panning", (-2, chain_path, panning))

        except Exception as e:
            self.logger.error(f"Erreur dans handle_chain_panning_learning: {str(e)}")

    def handle_chain_mute_learning(self, params):
        """
        Gère les messages reçus sur /live/chain/learning/mute
        params: (-2, chain_path)
        """
        try:
            # Extraire le chemin de la chaîne
            track_id = int(params[0])
            chain_path = params[1]  # chain_path est une liste d'indices
            
            # Récupérer la chaîne à partir du chemin
            chain = self._get_chain_by_path(chain_path)
            if not chain:
                self.logger.warning(f"Chaîne introuvable avec le chemin: {chain_path}")
                return

            # Obtenir l'état de mute
            mute_value = 1 if chain.mute else 0

            # Envoi du message avec l'état de mute et le chemin de la chaîne
            self.osc_server.send("/live/chain/learning/mute", (-2, chain_path, mute_value))

        except Exception as e:
            self.logger.error(f"Erreur dans handle_chain_mute_learning: {str(e)}")

    def handle_chain_solo_learning(self, params):
        """
        Gère les messages reçus sur /live/chain/learning/solo
        params: (-2, chain_path)
        """
        try:
            # Extraire le chemin de la chaîne
            track_id = int(params[0])
            chain_path = params[1]  # chain_path est une liste d'indices
            
            # Récupérer la chaîne à partir du chemin
            chain = self._get_chain_by_path(chain_path)
            if not chain:
                self.logger.warning(f"Chaîne introuvable avec le chemin: {chain_path}")
                return

            # Obtenir l'état de solo
            solo_value = 1 if chain.solo else 0

            # Envoi du message avec l'état de solo et le chemin de la chaîne
            self.osc_server.send("/live/chain/learning/solo", (-2, chain_path, solo_value))

        except Exception as e:
            self.logger.error(f"Erreur dans handle_chain_solo_learning: {str(e)}")

    def _get_chain_by_path(self, chain_path):
        """
        Récupère une chaîne à partir de son chemin.
        Le chemin est une liste d'indices [rack_idx, chain_idx, ...]
        """
        try:
            if not chain_path or len(chain_path) < 2:
                self.logger.warning(f"Chemin de chaîne invalide ou trop court: {chain_path}")
                return None

            selected_track = self.song.view.selected_track
            if not selected_track:
                self.logger.warning("Aucune piste sélectionnée")
                return None

            # Récupérer la liste des devices visibles
            visible_devices = self.manager.get_visible_devices(selected_track)
            self.logger.debug(f"Devices visibles dans _get_chain_by_path: {[d.name for d in visible_devices]}")

            rack_idx = chain_path[0]
            chain_idx = chain_path[1]

            # Vérifier si l'index du rack est valide
            if not (0 <= rack_idx < len(visible_devices)):
                self.logger.warning(f"Index de rack invalide: {rack_idx}. Nombre de devices visibles: {len(visible_devices)}")
                return None

            rack = visible_devices[rack_idx]
            if not hasattr(rack, 'chains'):
                self.logger.warning(f"Le device à l'index {rack_idx} ({rack.name}) n'est pas un rack")
                return None

            # Vérifier si l'index de la chaîne est valide
            if not (0 <= chain_idx < len(rack.chains)):
                self.logger.warning(f"Index de chaîne invalide: {chain_idx}. Nombre de chaînes dans {rack.name}: {len(rack.chains)}")
                return None

            chain = rack.chains[chain_idx]
            
            # Si nous avons plus de chemin à parcourir
            current_container = chain
            current_idx = 2
            
            while current_idx < len(chain_path):
                # Vérifier si l'élément actuel est un rack
                device_idx = chain_path[current_idx]
                
                # Vérifier si l'index du device est valide
                if not (0 <= device_idx < len(current_container.devices)):
                    self.logger.warning(f"Index de device invalide: {device_idx} à la position {current_idx} du chemin")
                    return None
                    
                device = current_container.devices[device_idx]
                current_idx += 1
                
                # Si nous sommes à la fin du chemin, retourner le device actuel
                if current_idx >= len(chain_path):
                    break
                    
                # Vérifier si le device est un rack
                if not hasattr(device, 'chains'):
                    self.logger.warning(f"Le device à l'index {device_idx} n'est pas un rack")
                    return None
                    
                # Récupérer l'index de la chaîne
                chain_idx = chain_path[current_idx]
                
                # Vérifier si l'index de la chaîne est valide
                if not (0 <= chain_idx < len(device.chains)):
                    self.logger.warning(f"Index de chaîne invalide: {chain_idx} à la position {current_idx} du chemin")
                    return None
                    
                # Passer à la chaîne suivante
                current_container = device.chains[chain_idx]
                chain = current_container
                current_idx += 1

            return chain

        except Exception as e:
            self.logger.error(f"Erreur dans _get_chain_by_path: {str(e)}")
            return None

    def _get_selected_chain(self):
        """
        Méthode utilitaire pour récupérer la chaîne sélectionnée
        """
        # Récupération de la piste sélectionnée
        selected_track = self.song.view.selected_track
        if not selected_track:
            self.logger.warning("Aucune piste sélectionnée")
            return None

        # Récupération du device sélectionné
        selected_device = selected_track.view.selected_device
        if not selected_device:
            self.logger.warning("Aucun device sélectionné")
            return None

        # Vérification que le device est un rack (possède des chaînes)
        if not hasattr(selected_device, 'chains') or not selected_device.chains:
            self.logger.warning("Le device sélectionné n'est pas un rack ou ne contient pas de chaînes")
            return None

        # Récupération de la chaîne sélectionnée
        selected_chain = selected_device.view.selected_chain
        if not selected_chain:
            self.logger.warning("Aucune chaîne sélectionnée dans le rack")
            return None

        return selected_chain

    def _get_chain_path(self, chain):
        """
        Récupère le chemin complet d'une chaîne sous forme de liste d'indices.
        """
        try:
            # Récupérer la piste sélectionnée
            selected_track = self.song.view.selected_track
            if not selected_track:
                self.logger.warning("Aucune piste sélectionnée")
                return []

            # Récupérer la liste des devices visibles une seule fois
            visible_devices = self.manager.get_visible_devices(selected_track)
            self.logger.debug(f"Devices visibles: {[d.name for d in visible_devices]}")
            self.logger.debug(f"Recherche du chemin pour la chaîne: {chain.name}")
            
            def find_chain_path(device_list, target_chain):
                for rack_index, device in enumerate(device_list):
                    if hasattr(device, 'chains'):
                        # Vérifier si la chaîne cible est directement dans ce rack
                        for chain_index, current_chain in enumerate(device.chains):
                            if current_chain == target_chain:
                                self.logger.debug(f"Chaîne trouvée dans le rack {device.name} à l'index {rack_index}, chain_index {chain_index}")
                                return [rack_index, chain_index]
                            
                            # Chercher dans les devices de cette chaîne
                            if current_chain.devices:
                                deeper_path = find_chain_path(current_chain.devices, target_chain)
                                if deeper_path:
                                    return [rack_index, chain_index] + deeper_path
                return None

            path = find_chain_path(visible_devices, chain) or []
            self.logger.debug(f"Chemin final trouvé pour la chaîne {chain.name}: {path}")
            return path
            
        except Exception as e:
            self.logger.error(f"Erreur dans _get_chain_path: {str(e)}")
            return []
            