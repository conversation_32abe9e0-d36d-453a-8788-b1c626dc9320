"""
Gestion des slots pour le mode learn
Fonctions pour la vérification des doublons, suppression et validation des slots
"""


def _clean_slot_listeners(self, learn_slot: int) -> None:
    """Nettoie complètement tous les listeners d'un slot spécifique avant réassignation"""
    self.logger.info(f"Nettoyage complet des listeners pour le slot {learn_slot}")
    
    if learn_slot not in self.learn_slots:
        self.logger.debug(f"Slot {learn_slot} n'existe pas, rien à nettoyer")
        return
    
    slot_data = self.learn_slots[learn_slot]
    track = slot_data.get("track")
    device = slot_data.get("device")
    chain = slot_data.get("chain")
    param_type = slot_data.get("param_type")
    track_name = f"track{learn_slot}"
    
    self.logger.debug(f"Nettoyage slot {learn_slot}: type={param_type}, track={track.name if track else None}, device={device.name if device else None}")
    
    # Nettoyer selon le type de paramètre
    if track and param_type:
        try:
            if param_type == 1:  # Volume
                listener_key = (track_name, "volume")
                if listener_key in self.learn_listeners:
                    track.mixer_device.volume.remove_value_listener(self.learn_listeners[listener_key])
                    del self.learn_listeners[listener_key]
                    self.logger.debug(f"Listener volume supprimé pour slot {learn_slot}")

            elif param_type == 2:  # Pan
                listener_key = (track_name, "panning")
                if listener_key in self.learn_listeners:
                    track.mixer_device.panning.remove_value_listener(self.learn_listeners[listener_key])
                    del self.learn_listeners[listener_key]
                    self.logger.debug(f"Listener pan supprimé pour slot {learn_slot}")

            elif param_type == 3:  # Send
                send_index = slot_data.get("send_index")
                if send_index is not None:
                    listener_key = (track_name, f"send_{send_index}")
                    if listener_key in self.learn_listeners:
                        track.mixer_device.sends[send_index].remove_value_listener(self.learn_listeners[listener_key])
                        del self.learn_listeners[listener_key]
                        self.logger.debug(f"Listener send {send_index} supprimé pour slot {learn_slot}")

            elif param_type == 4:  # Device Parameter
                if device:
                    device_id = id(device)
                    param_index = slot_data.get("param_index")



                    # Nettoyer avec les nouvelles clés (avec device_id)
                    listener_key = f"learn_{track_name}_device_param_{param_index}_{device_id}"
                    if listener_key in self.learn_listeners:
                        if param_index is not None and param_index < len(device.parameters):
                            device.parameters[param_index].remove_value_listener(self.learn_listeners[listener_key])
                            del self.learn_listeners[listener_key]
                            self.logger.debug(f"Listener device param {param_index} supprimé pour slot {learn_slot} (nouveau format)")

                    # Nettoyer avec les anciennes clés (compatibilité)
                    old_listener_key = f"learn_{track_name}_device_param_{param_index}"
                    if old_listener_key in self.learn_listeners:
                        if param_index is not None and param_index < len(device.parameters):
                            device.parameters[param_index].remove_value_listener(self.learn_listeners[old_listener_key])
                            del self.learn_listeners[old_listener_key]
                            self.logger.debug(f"Listener device param {param_index} supprimé pour slot {learn_slot} (ancien format)")

                    # Nettoyer TOUS les listeners de device params pour ce slot (sécurité)
                    keys_to_remove = []
                    for key in self.learn_listeners.keys():
                        if isinstance(key, str) and key.startswith(f"learn_{track_name}_device_param_"):
                            keys_to_remove.append(key)

                    for key in keys_to_remove:
                        try:
                            # Extraire l'index du paramètre
                            if key.endswith(f"_{device_id}"):
                                parts = key.split("_")
                                param_idx = int(parts[-2])
                            else:
                                param_idx = int(key.split("_")[-1])

                            if param_idx < len(device.parameters):
                                device.parameters[param_idx].remove_value_listener(self.learn_listeners[key])
                                del self.learn_listeners[key]
                                self.logger.debug(f"Listener device param {param_idx} supprimé (nettoyage sécurité)")
                        except Exception as e:
                            self.logger.error(f"Erreur lors du nettoyage sécurité: {e}")

                    # Nettoyer les callbacks du device
                    callback_key = f"learn_{track_name}_device_callbacks"
                    if callback_key in self.learn_listeners:
                        callbacks = self.learn_listeners[callback_key]
                        try:
                            device.remove_name_listener(callbacks["device_name"])
                            track.remove_name_listener(callbacks["track_name"])
                            track.remove_color_listener(callbacks["track_color"])
                            del self.learn_listeners[callback_key]
                            self.logger.debug(f"Callbacks device supprimés pour slot {learn_slot}")
                        except Exception as e:
                            self.logger.error(f"Erreur lors de la suppression des callbacks device: {e}")

            # Ajouter le nettoyage pour les autres types (chain, mute, solo, etc.)
            elif param_type in [5, 6, 9, 10]:  # Chain-based
                if chain:
                    # Nettoyer selon le type de paramètre de chaîne
                    if param_type == 5:  # Chain Volume
                        listener_key = (track_name, "chain_volume")
                    elif param_type == 6:  # Chain Pan
                        listener_key = (track_name, "chain_panning")
                    elif param_type == 9:  # Chain Mute
                        listener_key = (track_name, "chain_mute")
                    elif param_type == 10:  # Chain Solo
                        listener_key = (track_name, "chain_solo")
                    
                    if listener_key in self.learn_listeners:
                        if param_type == 5:
                            chain.mixer_device.volume.remove_value_listener(self.learn_listeners[listener_key])
                        elif param_type == 6:
                            chain.mixer_device.panning.remove_value_listener(self.learn_listeners[listener_key])
                        elif param_type == 9:
                            chain.remove_mute_listener(self.learn_listeners[listener_key])
                        elif param_type == 10:
                            chain.remove_solo_listener(self.learn_listeners[listener_key])
                        
                        del self.learn_listeners[listener_key]
                        self.logger.debug(f"Listener chain type {param_type} supprimé pour slot {learn_slot}")
                    
                    # Nettoyer les callbacks de la chaîne
                    callback_key = f"learn_{track_name}_chain_callbacks"
                    if callback_key in self.learn_listeners:
                        callbacks = self.learn_listeners[callback_key]
                        try:
                            chain.remove_name_listener(callbacks["chain_name"])
                            track.remove_name_listener(callbacks["track_name"])
                            track.remove_color_listener(callbacks["track_color"])
                            chain.canonical_parent.remove_chains_listener(callbacks["chains_changed"])
                            del self.learn_listeners[callback_key]
                            self.logger.debug(f"Callbacks chain supprimés pour slot {learn_slot}")
                        except Exception as e:
                            self.logger.error(f"Erreur lors de la suppression des callbacks chain: {e}")

            elif param_type == 7:  # Mute
                listener_key = f"track_{learn_slot}_mute"
                if listener_key in self.learn_listeners:
                    track.remove_mute_listener(self.learn_listeners[listener_key])
                    del self.learn_listeners[listener_key]
                    self.logger.debug(f"Listener mute supprimé pour slot {learn_slot}")

            elif param_type == 8:  # Solo
                listener_key = f"track_{learn_slot}_solo"
                if listener_key in self.learn_listeners:
                    track.remove_solo_listener(self.learn_listeners[listener_key])
                    del self.learn_listeners[listener_key]
                    self.logger.debug(f"Listener solo supprimé pour slot {learn_slot}")

        except Exception as e:
            self.logger.error(f"Erreur lors du nettoyage des listeners pour slot {learn_slot}: {e}")

    # Nettoyer les listeners de base (nom et couleur de piste)
    try:
        color_key = f"learn_{track_name}_color"
        name_key = f"learn_{track_name}_name"
        
        if color_key in self.learn_listeners and track:
            track.remove_color_listener(self.learn_listeners[color_key])
            del self.learn_listeners[color_key]
            self.logger.debug(f"Listener couleur piste supprimé pour slot {learn_slot}")
        
        if name_key in self.learn_listeners and track:
            track.remove_name_listener(self.learn_listeners[name_key])
            del self.learn_listeners[name_key]
            self.logger.debug(f"Listener nom piste supprimé pour slot {learn_slot}")
            
    except Exception as e:
        self.logger.error(f"Erreur lors de la suppression des listeners de base: {e}")

    # Réinitialiser complètement le slot
    self.learn_slots[learn_slot] = {
        "track": None,
        "device": None,
        "chain": None,
        "param_type": None,
        "param_index": None,
        "send_index": None,
        "chain_path": None
    }
    
    self.logger.info(f"Nettoyage complet terminé pour slot {learn_slot}. Listeners restants: {len(self.learn_listeners)}")


def _debug_listeners_state(self) -> None:
    """Affiche l'état actuel des listeners pour diagnostiquer les problèmes"""
    self.logger.info("=== DEBUG: État des listeners ===")
    self.logger.info(f"Nombre total de listeners: {len(self.learn_listeners)}")
    
    # Grouper les listeners par slot
    listeners_by_slot = {}
    for key in self.learn_listeners.keys():
        # Vérifier que key est une string avant d'utiliser startswith
        if isinstance(key, str) and key.startswith("learn_track"):
            try:
                slot_num = int(key.split("_")[2].replace("track", ""))
                if slot_num not in listeners_by_slot:
                    listeners_by_slot[slot_num] = []
                listeners_by_slot[slot_num].append(key)
            except:
                self.logger.debug(f"Impossible d'extraire le numéro de slot de: {key}")
        else:
            self.logger.debug(f"Clé non-string ou format inattendu: {key} (type: {type(key)})")
    
    # Afficher les listeners par slot
    for slot_num in sorted(listeners_by_slot.keys()):
        slot_data = self.learn_slots.get(slot_num, {})
        track = slot_data.get("track")
        device = slot_data.get("device")
        param_type = slot_data.get("param_type")
        
        self.logger.info(f"Slot {slot_num}: type={param_type}, track={track.name if track else None}, device={device.name if device else None}")
        for listener_key in listeners_by_slot[slot_num]:
            self.logger.info(f"  - {listener_key}")
    
    # Afficher les listeners orphelins
    orphan_listeners = []
    for key in self.learn_listeners.keys():
        if not (isinstance(key, str) and key.startswith("learn_track")):
            orphan_listeners.append(key)
    
    if orphan_listeners:
        self.logger.info("Listeners orphelins:")
        for key in orphan_listeners:
            self.logger.info(f"  - {key}")
    
    self.logger.info("=== FIN DEBUG ===")


def _check_duplicates(self, learn_slot: int, param_type: int, track, chain=None, device=None, param_index=None, send_index=None) -> None:
    """Vérifie et supprime les doublons dans les slots d'apprentissage"""
    self.logger.info(f"=== DEBUT _check_duplicates ===")
    self.logger.info(f"Checking duplicates for slot {learn_slot}, type {param_type}")
    self.logger.info(f"Input params - Track: {track.name if track else None}, Device: {device.name if device else None}, Chain: {chain.name if chain else None}")
    self.logger.info(f"Param index: {param_index}, Send index: {send_index}")

    """ # Debug: afficher l'état de tous les slots AVANT la vérification
    self.logger.info("État actuel de tous les slots:")
    for slot_idx, slot_data in self.learn_slots.items():
        slot_track = slot_data.get("track")
        slot_device = slot_data.get("device")
        slot_param_type = slot_data.get("param_type")
        slot_param_index = slot_data.get("param_index")
        self.logger.info(f"  Slot {slot_idx}: type={slot_param_type}, track={slot_track.name if slot_track else None}, device={slot_device.name if slot_device else None}, param_index={slot_param_index}") """

    for slot_index, slot_data in self.learn_slots.items():
        # Ne pas vérifier le slot actuel
        if slot_index == learn_slot:
            continue

        slot_param_type = slot_data.get("param_type")
        slot_device = slot_data.get("device")
        slot_chain = slot_data.get("chain")
        slot_track = slot_data.get("track")

        """ self.logger.info(f"Checking slot {slot_index}:")
        self.logger.info(f"- Type: {slot_param_type}")
        self.logger.info(f"- Device: {slot_device.name if slot_device else None}")
        self.logger.info(f"- Chain: {slot_chain.name if slot_chain else None}")
        self.logger.info(f"- Track: {slot_track.name if slot_track else None}") """

        # Si le slot est vide ou les types ne correspondent pas
        """ if slot_param_type is None or slot_param_type != param_type:
            self.logger.info(f"Skipping slot {slot_index}: slot_param_type={slot_param_type}, param_type={param_type}")
            continue """

        # Types 5, 6, 9 et 10: Chain Volume, Chain Pan, Chain Mute, Chain Solo
        if param_type in [5, 6, 9, 10] and chain and slot_chain:
            try:
                # Vérifier si c'est exactement la même chaîne
                if chain == slot_chain:
                    self.logger.info(f"Found duplicate chain parameter in slot {slot_index}")
                    self.delete_single_slot(slot_index)
            except Exception as e:
                self.logger.error(f"Error checking chain duplicate: {e}")

        # Type 4: Device Parameter - Amélioration de la vérification
        elif param_type == 4 and device and slot_device:
            try:
                # Vérifier si c'est exactement le même device et le même paramètre
                device_match = (device == slot_device)
                param_match = (param_index == slot_data.get("param_index"))

                self.logger.info(f"Device comparison: {device.name} == {slot_device.name} -> {device_match}")
                self.logger.info(f"Param comparison: {param_index} == {slot_data.get('param_index')} -> {param_match}")

                if device_match and param_match:
                    self.logger.info(f"Found duplicate device parameter in slot {slot_index}: device '{device.name}', param {param_index}")
                    self.delete_single_slot(slot_index)
            except Exception as e:
                self.logger.error(f"Error checking device duplicate: {e}")

        # Types 1, 2, 3, 7, 8: Track-based parameters (Volume, Pan, Send, Mute, Solo)
        elif track and slot_track and track == slot_track:
            if param_type in [1, 2, 7, 8]:  # Volume, Pan, Mute, Solo
                self.logger.info(f"Found duplicate track parameter in slot {slot_index}")
                self.delete_single_slot(slot_index)
            elif param_type == 3 and send_index == slot_data.get("send_index"):  # Send
                self.logger.info(f"Found duplicate send parameter in slot {slot_index}")
                self.delete_single_slot(slot_index)

    self.logger.info(f"=== FIN _check_duplicates ===")


def _resolve_device_learn_conflicts(self, device, param_index):
    """Résout les conflits entre les listeners du learn mode et ceux de device_learn.py"""
    try:
        # Accéder au device_learn handler s'il existe
        if hasattr(self.manager, 'device_learn') and hasattr(self.manager.device_learn, 'learn_listeners'):
            device_learn_listeners = self.manager.device_learn.learn_listeners

            # Chercher les listeners conflictuels dans device_learn.py
            # Les clés dans device_learn.py sont au format "device_{device_index}_param_{param_index}"
            conflicting_keys = []
            for key in device_learn_listeners.keys():
                if key.startswith("device_") and key.endswith(f"_param_{param_index}"):
                    # Vérifier si ce listener correspond au même device
                    try:
                        callback = device_learn_listeners[key]
                        if hasattr(callback, 'parameter') and callback.parameter in device.parameters:
                            if device.parameters.index(callback.parameter) == param_index:
                                conflicting_keys.append(key)
                                self.logger.info(f"Conflit détecté avec device_learn listener: {key}")
                    except Exception as e:
                        self.logger.debug(f"Erreur lors de la vérification du conflit pour {key}: {e}")

            # Supprimer les listeners conflictuels de device_learn.py
            for key in conflicting_keys:
                try:
                    callback = device_learn_listeners[key]
                    if hasattr(callback, 'parameter') and hasattr(callback, 'is_active'):
                        callback.is_active = False
                        parameter = callback.parameter
                        if parameter.value_has_listener(callback):
                            parameter.remove_value_listener(callback)
                            self.logger.info(f"Listener device_learn supprimé: {key}")
                    del device_learn_listeners[key]
                except Exception as e:
                    self.logger.error(f"Erreur lors de la suppression du listener device_learn {key}: {e}")

    except Exception as e:
        self.logger.error(f"Erreur lors de la résolution des conflits device_learn: {e}")


def delete_single_slot(self, slot_index):
    """Supprime un slot d'apprentissage spécifique"""
    try:
        if isinstance(slot_index, list) and len(slot_index) > 0:
            slot_index = int(slot_index[0])
        else:
            slot_index = int(slot_index)
    except (ValueError, TypeError):
        self.logger.error(f"Index de slot invalide: {slot_index}")
        return

    if slot_index not in self.learn_slots:
        self.logger.warning(f"Slot {slot_index} n'existe pas")
        return

    slot_data = self.learn_slots[slot_index]
    track = slot_data.get("track")
    device = slot_data.get("device")
    chain = slot_data.get("chain")
    param_type = slot_data.get("param_type")
    track_name = f"track{slot_index}"

    self.logger.info(f"Suppression du slot {slot_index} (type: {param_type})")

    # Supprimer les listeners selon le type
    if track and param_type:
        try:
            if param_type == 1:  # Volume
                if (track_name, "volume") in self.learn_listeners:
                    track.mixer_device.volume.remove_value_listener(self.learn_listeners[(track_name, "volume")])
                    del self.learn_listeners[(track_name, "volume")]

            elif param_type == 2:  # Pan
                if (track_name, "panning") in self.learn_listeners:
                    track.mixer_device.panning.remove_value_listener(self.learn_listeners[(track_name, "panning")])
                    del self.learn_listeners[(track_name, "panning")]

            elif param_type == 3:  # Send
                send_index = slot_data.get("send_index")
                if send_index is not None and (track_name, f"send_{send_index}") in self.learn_listeners:
                    track.mixer_device.sends[send_index].remove_value_listener(
                        self.learn_listeners[(track_name, f"send_{send_index}")])
                    del self.learn_listeners[(track_name, f"send_{send_index}")]

            elif param_type == 4:  # Device Parameter
                if device:
                    device_id = id(device)
                    for key in list(self.learn_listeners.keys()):
                        # Vérifier que key est une string avant d'utiliser startswith
                        if not isinstance(key, str):
                            continue

                        # Gestion des nouveaux formats de clés avec device_id
                        if (key.startswith(f"learn_{track_name}_device_param_") and
                            key.endswith(f"_{device_id}")):
                            try:
                                # Extraire param_index de la clé format: learn_trackX_device_param_Y_DEVICE_ID
                                parts = key.split("_")
                                param_index = int(parts[-2])  # L'avant-dernier élément est param_index
                                if param_index < len(device.parameters):
                                    device.parameters[param_index].remove_value_listener(
                                        self.learn_listeners[key])
                                    del self.learn_listeners[key]
                            except Exception as e:
                                self.logger.error(f"Error removing device parameter listener: {e}")
                        # Compatibilité avec l'ancien format
                        elif key.startswith(f"learn_{track_name}_device_param_"):
                            try:
                                param_index = int(key.split("_")[-1])
                                if param_index < len(device.parameters):
                                    device.parameters[param_index].remove_value_listener(
                                        self.learn_listeners[key])
                                    del self.learn_listeners[key]
                            except Exception as e:
                                self.logger.error(f"Error removing device parameter listener: {e}")

                    # Supprimer les callbacks du device
                    callbacks = self.learn_listeners.get(f"learn_{track_name}_device_callbacks", {})
                    if callbacks:
                        device.remove_name_listener(callbacks["device_name"])
                        track.remove_name_listener(callbacks["track_name"])
                        track.remove_color_listener(callbacks["track_color"])
                        del self.learn_listeners[f"learn_{track_name}_device_callbacks"]

            elif param_type == 5:  # Chain Volume
                if chain:
                    if (track_name, "chain_volume") in self.learn_listeners:
                        chain.mixer_device.volume.remove_value_listener(
                            self.learn_listeners[(track_name, "chain_volume")])
                        del self.learn_listeners[(track_name, "chain_volume")]

                    # Supprimer les callbacks de la chaîne
                    callbacks = self.learn_listeners.get(f"learn_{track_name}_chain_callbacks", {})
                    if callbacks:
                        chain.remove_name_listener(callbacks["chain_name"])
                        track.remove_name_listener(callbacks["track_name"])
                        track.remove_color_listener(callbacks["track_color"])
                        chain.canonical_parent.remove_chains_listener(callbacks["chains_changed"])
                        del self.learn_listeners[f"learn_{track_name}_chain_callbacks"]

            elif param_type == 6:  # Chain Pan
                if chain:
                    if (track_name, "chain_panning") in self.learn_listeners:
                        chain.mixer_device.panning.remove_value_listener(
                            self.learn_listeners[(track_name, "chain_panning")])
                        del self.learn_listeners[(track_name, "chain_panning")]

                    # Supprimer les callbacks de la chaîne
                    callbacks = self.learn_listeners.get(f"learn_{track_name}_chain_callbacks", {})
                    if callbacks:
                        chain.remove_name_listener(callbacks["chain_name"])
                        track.remove_name_listener(callbacks["track_name"])
                        track.remove_color_listener(callbacks["track_color"])
                        chain.canonical_parent.remove_chains_listener(callbacks["chains_changed"])
                        del self.learn_listeners[f"learn_{track_name}_chain_callbacks"]

            elif param_type == 7:  # Mute
                listener_key = f"track_{slot_index}_mute"
                if listener_key in self.learn_listeners:
                    track.remove_mute_listener(self.learn_listeners[listener_key])
                    del self.learn_listeners[listener_key]

            elif param_type == 8:  # Solo
                listener_key = f"track_{slot_index}_solo"
                if listener_key in self.learn_listeners:
                    track.remove_solo_listener(self.learn_listeners[listener_key])
                    del self.learn_listeners[listener_key]

            elif param_type == 9:  # Chain Mute
                if chain:
                    if (track_name, "chain_mute") in self.learn_listeners:
                        chain.remove_mute_listener(
                            self.learn_listeners[(track_name, "chain_mute")])
                        del self.learn_listeners[(track_name, "chain_mute")]

                    # Supprimer les callbacks de la chaîne
                    callbacks = self.learn_listeners.get(f"learn_{track_name}_chain_callbacks", {})
                    if callbacks:
                        chain.remove_name_listener(callbacks["chain_name"])
                        track.remove_name_listener(callbacks["track_name"])
                        track.remove_color_listener(callbacks["track_color"])
                        chain.canonical_parent.remove_chains_listener(callbacks["chains_changed"])
                        del self.learn_listeners[f"learn_{track_name}_chain_callbacks"]

            elif param_type == 10:  # Chain Solo
                if chain:
                    if (track_name, "chain_solo") in self.learn_listeners:
                        chain.remove_solo_listener(
                            self.learn_listeners[(track_name, "chain_solo")])
                        del self.learn_listeners[(track_name, "chain_solo")]

                    # Supprimer les callbacks de la chaîne
                    callbacks = self.learn_listeners.get(f"learn_{track_name}_chain_callbacks", {})
                    if callbacks:
                        chain.remove_name_listener(callbacks["chain_name"])
                        track.remove_name_listener(callbacks["track_name"])
                        track.remove_color_listener(callbacks["track_color"])
                        chain.canonical_parent.remove_chains_listener(callbacks["chains_changed"])
                        del self.learn_listeners[f"learn_{track_name}_chain_callbacks"]

        except Exception as e:
            self.logger.error(f"Erreur lors de la suppression des listeners pour le slot {slot_index}: {e}")

    # Supprimer les listeners de base de la piste
    try:
        if f"learn_{track_name}_color" in self.learn_listeners:
            if track:
                track.remove_color_listener(self.learn_listeners[f"learn_{track_name}_color"])
            del self.learn_listeners[f"learn_{track_name}_color"]

        if f"learn_{track_name}_name" in self.learn_listeners:
            if track:
                track.remove_name_listener(self.learn_listeners[f"learn_{track_name}_name"])
            del self.learn_listeners[f"learn_{track_name}_name"]
    except Exception as e:
        self.logger.error(f"Erreur lors de la suppression des listeners de base pour le slot {slot_index}: {e}")

    # Réinitialiser le slot
    self.learn_slots[slot_index] = {
        "track": None,
        "device": None,
        "param_type": None,
        "param_index": None
    }

    # Envoyer la notification de suppression
    self.logger.info(f"Envoi du message OSC /live/learn/slot/cleared pour le slot {slot_index}")
    self.osc_server.send("/live/learn/slot/cleared", (slot_index,))
    self.logger.info(f"Slot {slot_index} supprimé et réinitialisé")


def verify_learn_slots_validity(self, force_device_recheck=False):
    """Vérifie la validité de tous les slots d'apprentissage

    Args:
        force_device_recheck: Si True, force la revérification des slots de device même s'ils semblent valides
    """
    self.logger.info(f"Vérification de la validité des slots d'apprentissage (force_device_recheck={force_device_recheck})")

    slots_to_clear = []

    for slot_index, slot_data in self.learn_slots.items():
        param_type = slot_data.get("param_type")
        if param_type is None:
            continue  # Slot vide, pas de problème

        track = slot_data.get("track")
        device = slot_data.get("device")
        chain = slot_data.get("chain")

        is_valid = True

        try:
            # Vérifier la validité selon le type
            if param_type in [1, 2, 3, 7, 8]:  # Paramètres de piste
                if not track or not hasattr(track, 'name'):
                    is_valid = False
                    self.logger.warning(f"Slot {slot_index}: piste invalide")

            elif param_type == 4:  # Paramètre de device
                if not device or not hasattr(device, 'name'):
                    is_valid = False
                    self.logger.warning(f"Slot {slot_index}: device invalide")
                elif not track or not hasattr(track, 'name'):
                    is_valid = False
                    self.logger.warning(f"Slot {slot_index}: piste du device invalide")
                elif force_device_recheck:
                    # Vérification supplémentaire: le device est-il toujours dans la track?
                    visible_devices = self.manager.get_visible_devices(track)
                    if device not in visible_devices:
                        is_valid = False
                        self.logger.warning(f"Slot {slot_index}: device {device.name} n'est plus dans la track {track.name}")
                    else:
                        # Le device est toujours là, mais sa position a peut-être changé
                        # Pour être sûr, on force la reconfiguration du slot
                        self.logger.info(f"Slot {slot_index}: device {device.name} toujours présent, mais position possiblement changée - reconfiguration forcée")
                        is_valid = False  # Force la reconfiguration
                    
            elif param_type in [5, 6, 9, 10]:  # Paramètres de chaîne
                if not chain or not hasattr(chain, 'name'):
                    is_valid = False
                    self.logger.warning(f"Slot {slot_index}: chaîne invalide")
                elif not track or not hasattr(track, 'name'):
                    is_valid = False
                    self.logger.warning(f"Slot {slot_index}: piste de la chaîne invalide")
                    
        except Exception as e:
            self.logger.error(f"Erreur lors de la vérification du slot {slot_index}: {e}")
            is_valid = False
            
        if not is_valid:
            slots_to_clear.append(slot_index)
    
    # Nettoyer les slots invalides
    for slot_index in slots_to_clear:
        self.logger.info(f"Nettoyage du slot invalide {slot_index}")
        self.delete_single_slot(slot_index)
    
    if slots_to_clear:
        self.logger.info(f"Nettoyage terminé: {len(slots_to_clear)} slots supprimés")
    else:
        self.logger.info("Tous les slots sont valides")


def send_learn_slot_reference(self, learn_slot: int):
    """Envoie les informations de référence d'un slot d'apprentissage"""
    try:
        slot_data = self.learn_slots.get(learn_slot, {})
        param_type = slot_data.get("param_type")
        
        if param_type is None:
            # Slot vide
            self.osc_server.send("/live/learn/slot/reference", (learn_slot, "", "", 0))
            return
            
        track = slot_data.get("track")
        device = slot_data.get("device")
        chain = slot_data.get("chain")
        
        track_name = track.name if track else ""
        track_color = track.color if track else 0
        
        if param_type == 4 and device:  # Device parameter
            device_name = device.name if device else ""
            self.osc_server.send("/live/learn/slot/reference", 
                               (learn_slot, track_name, device_name, track_color))
        elif param_type in [5, 6, 9, 10] and chain:  # Chain parameters
            chain_name = chain.name if chain else ""
            self.osc_server.send("/live/learn/slot/reference", 
                               (learn_slot, track_name, chain_name, track_color))
        else:  # Track parameters
            self.osc_server.send("/live/learn/slot/reference", 
                               (learn_slot, track_name, "", track_color))
                               
    except Exception as e:
        self.logger.error(f"Erreur lors de l'envoi de la référence du slot {learn_slot}: {e}")
        # Envoyer une référence vide en cas d'erreur
        self.osc_server.send("/live/learn/slot/reference", (learn_slot, "", "", 0))
